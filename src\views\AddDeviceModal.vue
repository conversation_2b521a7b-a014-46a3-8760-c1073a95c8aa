<template>
    <div class="support-page">
        <h1 class="page-title">支撑应用管理</h1>
        <button class="add-device-btn" @click="showModal = !showModal">添加设备</button>
        <Teleport to="body">
            <AddDeviceModal v-if="showModal" @close="showModal = false" />
        </Teleport>
        <!-- 其他静态内容 -->
    </div>
</template>

<script setup>
import AddDeviceModal from '../views/AddDeviceModal.vue'
import { ref } from 'vue'
const showModal = ref(false)
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.support-page {
    padding: $spacing-lg;
}

.page-title {
    font-size: 32px;
    margin-bottom: $spacing-md;
    color: $text-primary;
    font-weight: 600;
}

.add-device-btn {
    @include button-style($primary-color, $text-white);
}
</style>