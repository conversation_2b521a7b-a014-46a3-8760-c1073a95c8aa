<template>
    <aside class="side-nav">
        <!-- -->
        <ul class="nav-list">
            <li class="nav-item">
                <div class="nav-link">公共场所在线监测</div>
            </li>
            <li class="nav-item">
                <div class="nav-link">放射卫生在线监测</div>
            </li>
        </ul>
    </aside>
</template>

<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.side-nav {
    width: 350px;
    height: calc(100vh - 60px);
    background: linear-gradient(to right, RGBA(28, 72, 160, 1), RGBA(11, 35, 85, 1), RGBA(11, 35, 85, 1), rgba(11, 35, 83, 0.7));
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: $spacing-sm;
}

.nav-link {
    display: block;
    padding: $spacing-sm $spacing-md;
    color: $text-primary;
    text-decoration: none;
    border-radius: $border-radius-md;
    transition: background-color 0.3s ease;

    &:hover {
        background-color: $bg-secondary;
    }

    &.router-link-active {
        background-color: $primary-color;
        color: $text-white;
    }
}
</style>